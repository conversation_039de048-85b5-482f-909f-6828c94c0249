/**
 * Simplified State Store Implementation
 * Flat state structure with basic operations
 */

import { v4 as uuidv4 } from 'uuid';
import {
  SimplifiedState,
  ISimplifiedStateStore,
  IStorageAdapter,
  MemoryStorageAdapter,
  SystemEvent,
  SystemError,
  UsageStats,
  ContentItem,
  Review,
  EssentialEventType
} from './types';
import { Workflow, WorkflowExecution } from '../workflow/types';
import { errorHandler, ErrorType, ErrorSeverity } from '../utils/error-handler';

export class SimplifiedStateStore implements ISimplifiedStateStore {
  private static readonly STATE_KEY = 'system_state';
  private updateLock: Promise<void> = Promise.resolve();

  constructor(private storage: IStorageAdapter = new MemoryStorageAdapter()) {}

  // Basic state operations
  async get(): Promise<SimplifiedState | null> {
    try {
      let state = await this.storage.get(SimplifiedStateStore.STATE_KEY);

      // Ensure state structure is complete
      if (state && typeof state === 'object') {
        let needsUpdate = false;

        // Count existing data before any modifications to prevent data loss
        const existingExecutions = state.executions ? Object.keys(state.executions).length : 0;
        const existingWorkflows = state.workflows ? Object.keys(state.workflows).length : 0;

        // Only log this occasionally to reduce noise
        if (Math.random() < 0.1) {
          console.log(`🔍 State validation - existing data:`, {
            workflows: existingWorkflows,
            executions: existingExecutions,
            hasExecutions: !!state.executions,
            executionKeys: state.executions ? Object.keys(state.executions).slice(0, 5) : []
          });
        }

        // Initialize missing properties - but only if they don't exist
        // Use more defensive checks to prevent data loss
        if (!state.workflows || typeof state.workflows !== 'object') {
          state.workflows = {};
          needsUpdate = true;
        }
        if (!state.executions || typeof state.executions !== 'object') {
          state.executions = {};
          needsUpdate = true;
        }
        if (!state.content || typeof state.content !== 'object') {
          state.content = {};
          needsUpdate = true;
        }
        if (!state.reviews || typeof state.reviews !== 'object') {
          state.reviews = {};
          needsUpdate = true;
        }
        if (!state.approvalGates || typeof state.approvalGates !== 'object') {
          state.approvalGates = {};
          needsUpdate = true;
        }
        if (!state.reviewAssignments || typeof state.reviewAssignments !== 'object') {
          state.reviewAssignments = {};
          needsUpdate = true;
        }
        if (!state.artifactVersions || typeof state.artifactVersions !== 'object') {
          state.artifactVersions = {};
          needsUpdate = true;
        }
        if (!state.reviewers || typeof state.reviewers !== 'object') {
          state.reviewers = {};
          needsUpdate = true;
        }

        // Ensure system object exists
        if (!state.system) {
          state.system = {
            events: [],
            errors: [],
            usage: {
              totalWorkflows: 0,
              totalExecutions: 0,
              totalContent: 0,
              totalReviews: 0,
              aiUsage: {
                totalRequests: 0,
                totalTokens: 0,
                totalCost: 0,
                byProvider: {}
              },
              lastReset: new Date().toISOString()
            },
            config: {
              defaultAIProvider: 'openai',
              defaultAIModel: 'gpt-4',
              defaultReviewTimeout: 24,
              emailNotifications: true,
              rateLimits: {
                workflowsPerHour: 100,
                aiRequestsPerMinute: 60
              }
            }
          };
          needsUpdate = true;
        }

        // If we had to initialize any missing properties, persist the updated state
        // BUT ONLY if we're not losing existing data AND only if we actually need to update
        if (needsUpdate) {
          const finalExecutions = state.executions ? Object.keys(state.executions).length : 0;
          const finalWorkflows = state.workflows ? Object.keys(state.workflows).length : 0;

          console.log(`🔧 State update check:`, {
            needsUpdate,
            executionsBefore: existingExecutions,
            executionsAfter: finalExecutions,
            workflowsBefore: existingWorkflows,
            workflowsAfter: finalWorkflows,
            dataLoss: finalExecutions < existingExecutions || finalWorkflows < existingWorkflows,
            hasExistingData: existingExecutions > 0 || existingWorkflows > 0
          });

          // CRITICAL FIX: Don't update state if we have existing data and would lose it
          // This prevents the state validation from accidentally wiping out executions
          if (existingExecutions > 0 || existingWorkflows > 0) {
            console.log('🛡️ SKIPPING state update - preserving existing data');
          } else {
            // Only persist if we're not losing data OR if there's no existing data
            if (finalExecutions >= existingExecutions && finalWorkflows >= existingWorkflows) {
              await this.storage.set(SimplifiedStateStore.STATE_KEY, state);
              console.log('🔧 State structure updated and persisted to Redis');
            } else {
              console.error('❌ PREVENTED STATE UPDATE - would lose data!', {
                executionLoss: existingExecutions - finalExecutions,
                workflowLoss: existingWorkflows - finalWorkflows
              });
            }
          }
        }
      }

      return state;
    } catch (error) {
      const standardError = errorHandler.handleError(error, {
        operation: 'get',
        storageType: this.storage.constructor.name
      });
      throw standardError;
    }
  }

  async set(state: SimplifiedState): Promise<void> {
    try {
      state.lastUpdated = new Date().toISOString();
      state.version += 1;
      await this.storage.set(SimplifiedStateStore.STATE_KEY, state);
    } catch (error) {
      const standardError = errorHandler.handleError(error, {
        operation: 'set',
        storageType: this.storage.constructor.name,
        stateVersion: state.version
      });
      throw standardError;
    }
  }

  async update(updateFn: (state: SimplifiedState | null) => SimplifiedState | null): Promise<void> {
    // Chain this update operation after any pending updates
    this.updateLock = this.updateLock.then(async () => {
      try {
        const currentState = await this.get();
        const newState = updateFn(currentState);

        if (newState) {
          await this.set(newState);
        }
      } catch (error) {
        const standardError = errorHandler.handleError(error, {
          operation: 'update',
          storageType: this.storage.constructor.name
        });
        throw standardError;
      }
    });

    // Wait for this update to complete
    await this.updateLock;
  }

  // Initialize empty state
  async initialize(): Promise<void> {
    const existingState = await this.get();
    if (!existingState) {
      const initialState: SimplifiedState = {
        workflows: {},
        executions: {},
        content: {},
        reviews: {},
        approvalGates: {},
        reviewAssignments: {},
        artifactVersions: {},
        reviewers: {},
        system: {
          events: [],
          errors: [],
          usage: {
            totalWorkflows: 0,
            totalExecutions: 0,
            totalContent: 0,
            totalReviews: 0,
            aiUsage: {
              totalRequests: 0,
              totalTokens: 0,
              totalCost: 0,
              byProvider: {}
            },
            lastReset: new Date().toISOString()
          },
          config: {
            defaultAIProvider: 'openai',
            defaultAIModel: 'gpt-4',
            defaultReviewTimeout: 24,
            emailNotifications: true,
            rateLimits: {
              workflowsPerHour: 100,
              aiRequestsPerMinute: 60
            }
          }
        },
        lastUpdated: new Date().toISOString(),
        version: 1
      };

      await this.set(initialState);
    }
  }

  // Workflow operations
  async getWorkflow(id: string): Promise<Workflow | null> {
    try {
      const state = await this.get();
      if (!state) {
        console.warn(`⚠️ No state found when getting workflow ${id}`);
        return null;
      }

      // Ensure workflows object exists
      if (!state.workflows) {
        console.warn(`⚠️ No workflows object in state when getting workflow ${id}`);
        return null;
      }

      const workflow = state.workflows[id] || null;
      if (workflow) {
        console.log(`✅ Found workflow ${id}: ${workflow.name}`);
      } else {
        console.warn(`⚠️ Workflow ${id} not found in state. Available workflows: ${Object.keys(state.workflows).join(', ')}`);
      }

      return workflow;
    } catch (error) {
      console.error(`❌ Error getting workflow ${id}:`, error);
      throw error;
    }
  }

  async setWorkflow(workflow: Workflow): Promise<void> {
    const maxRetries = 3;
    let retries = 0;

    while (retries < maxRetries) {
      try {
        await this.update(state => {
          if (!state) {
            console.error('❌ State is null when trying to set workflow');
            // Initialize state if it doesn't exist
            return {
              workflows: { [workflow.id]: workflow },
              executions: {},
              content: {},
              reviews: {},
              approvalGates: {},
              reviewAssignments: {},
              artifactVersions: {},
              reviewers: {},
              system: {
                events: [],
                errors: [],
                usage: {
                  totalWorkflows: 1,
                  totalExecutions: 0,
                  totalContent: 0,
                  totalReviews: 0,
                  aiUsage: {
                    totalRequests: 0,
                    totalTokens: 0,
                    totalCost: 0,
                    byProvider: {}
                  },
                  lastReset: new Date().toISOString()
                },
                config: {
                  defaultAIProvider: 'openai',
                  defaultAIModel: 'gpt-4',
                  defaultReviewTimeout: 24,
                  emailNotifications: true,
                  rateLimits: {
                    workflowsPerHour: 100,
                    aiRequestsPerMinute: 60
                  }
                }
              },
              lastUpdated: new Date().toISOString(),
              version: 1
            };
          }

          // Ensure workflows object exists
          if (!state.workflows) {
            state.workflows = {};
          }

          const isNewWorkflow = !state.workflows[workflow.id];
          const currentWorkflowCount = Object.keys(state.workflows).length;

          const updatedState = {
            ...state,
            workflows: {
              ...state.workflows,
              [workflow.id]: workflow
            },
            system: {
              ...state.system,
              usage: {
                ...state.system.usage,
                totalWorkflows: isNewWorkflow ? currentWorkflowCount + 1 : currentWorkflowCount
              }
            },
            lastUpdated: new Date().toISOString()
          };

          console.log(`✅ Workflow ${workflow.id} ${isNewWorkflow ? 'created' : 'updated'} in state`);
          return updatedState;
        });

        // If we get here, the operation succeeded
        return;

      } catch (error) {
        retries++;
        console.warn(`⚠️ Retry ${retries}/${maxRetries} for setting workflow ${workflow.id}:`, error);

        if (retries >= maxRetries) {
          console.error(`❌ Failed to set workflow ${workflow.id} after ${maxRetries} retries:`, error);
          throw error;
        }

        // Wait a bit before retrying
        await new Promise(resolve => setTimeout(resolve, 10 * retries));
      }
    }
  }

  // Execution operations
  async getExecution(id: string): Promise<WorkflowExecution | null> {
    const state = await this.get();
    if (!state || !state.executions) {
      console.log(`⚠️ No state or executions found for execution ${id}`);
      return null;
    }

    const execution = state.executions[id] || null;
    const executionKeys = Object.keys(state.executions);

    console.log(`🔍 getExecution(${id}):`, {
      found: !!execution,
      totalExecutions: executionKeys.length,
      executionExists: id in state.executions,
      availableKeys: executionKeys.slice(0, 5),
      targetId: id
    });

    return execution;
  }

  async setExecution(execution: WorkflowExecution): Promise<void> {
    console.log(`🔧 Setting execution ${execution.id}:`, {
      id: execution.id,
      workflowId: execution.workflowId,
      status: execution.status,
      hasInputs: !!execution.inputs,
      hasOutputs: !!execution.outputs,
      hasStepResults: !!execution.stepResults,
      stepResultsCount: Object.keys(execution.stepResults || {}).length,
      progress: execution.progress,
      startedAt: execution.startedAt
    });

    await this.update(state => {
      if (!state) {
        console.warn(`⚠️ State is null when setting execution ${execution.id}`);
        return null;
      }

      // Ensure executions object exists
      if (!state.executions) {
        state.executions = {};
      }

      const isNew = !state.executions[execution.id];

      const updatedState = {
        ...state,
        executions: {
          ...state.executions,
          [execution.id]: execution
        },
        system: {
          ...state.system,
          usage: {
            ...state.system.usage,
            totalExecutions: isNew ? state.system.usage.totalExecutions + 1 : state.system.usage.totalExecutions
          }
        }
      };

      console.log(`✅ Execution ${execution.id} ${isNew ? 'created' : 'updated'} in state`);
      return updatedState;
    });

    // Emit event for execution status changes
    if (execution.status === 'running') {
      await this.addEvent({
        type: EssentialEventType.WORKFLOW_STARTED,
        data: { executionId: execution.id, workflowId: execution.workflowId }
      });
    } else if (execution.status === 'completed') {
      await this.addEvent({
        type: EssentialEventType.WORKFLOW_COMPLETED,
        data: { executionId: execution.id, workflowId: execution.workflowId }
      });
    } else if (execution.status === 'failed') {
      await this.addEvent({
        type: EssentialEventType.WORKFLOW_FAILED,
        data: { executionId: execution.id, workflowId: execution.workflowId, error: execution.error }
      });
    }
  }

  // Content operations
  async getContent(id: string): Promise<ContentItem | null> {
    const state = await this.get();
    if (!state || !state.content) {
      console.warn(`⚠️ No state or content object when getting content ${id}`);
      return null;
    }
    return state.content[id] || null;
  }

  async setContent(content: ContentItem): Promise<void> {
    await this.update(state => {
      if (!state) return null;

      // Ensure content object exists
      if (!state.content) {
        state.content = {};
      }

      const isNew = !state.content[content.id];

      return {
        ...state,
        content: {
          ...state.content,
          [content.id]: content
        },
        system: {
          ...state.system,
          usage: {
            ...state.system.usage,
            totalContent: isNew ? state.system.usage.totalContent + 1 : state.system.usage.totalContent
          }
        }
      };
    });

    // Emit event for content ready
    if (content.status === 'approved') {
      await this.addEvent({
        type: EssentialEventType.CONTENT_READY,
        data: { contentId: content.id, type: content.type }
      });
    }
  }

  // Review operations
  async getReview(id: string): Promise<Review | null> {
    const state = await this.get();
    return state?.reviews[id] || null;
  }

  async setReview(review: Review): Promise<void> {
    await this.update(state => {
      if (!state) return null;

      const isNew = !state.reviews[review.id];

      return {
        ...state,
        reviews: {
          ...state.reviews,
          [review.id]: review
        },
        system: {
          ...state.system,
          usage: {
            ...state.system.usage,
            totalReviews: isNew ? state.system.usage.totalReviews + 1 : state.system.usage.totalReviews
          }
        }
      };
    });

    // Emit events for review status changes
    if (review.status === 'pending') {
      await this.addEvent({
        type: EssentialEventType.REVIEW_NEEDED,
        data: { reviewId: review.id, contentId: review.contentId, type: review.type }
      });
    } else if (review.status === 'completed') {
      await this.addEvent({
        type: EssentialEventType.REVIEW_COMPLETE,
        data: { reviewId: review.id, contentId: review.contentId, decision: review.decision }
      });
    }
  }

  // Event operations
  async addEvent(event: Omit<SystemEvent, 'id' | 'timestamp'>): Promise<void> {
    const systemEvent: SystemEvent = {
      id: uuidv4(),
      timestamp: new Date().toISOString(),
      ...event
    };

    await this.update(state => {
      if (!state) return null;

      // Keep only last 1000 events to prevent memory issues
      const events = [...state.system.events, systemEvent].slice(-1000);

      return {
        ...state,
        system: {
          ...state.system,
          events
        }
      };
    });
  }

  async getEvents(limit: number = 100): Promise<SystemEvent[]> {
    const state = await this.get();
    if (!state) return [];

    return state.system.events
      .sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime())
      .slice(0, limit);
  }

  // Error operations
  async addError(error: Omit<SystemError, 'id' | 'timestamp'>): Promise<void> {
    const systemError: SystemError = {
      id: uuidv4(),
      timestamp: new Date().toISOString(),
      resolved: false,
      ...error
    };

    await this.update(state => {
      if (!state) return null;

      // Keep only last 500 errors
      const errors = [...state.system.errors, systemError].slice(-500);

      return {
        ...state,
        system: {
          ...state.system,
          errors
        }
      };
    });
  }

  async getErrors(resolved?: boolean): Promise<SystemError[]> {
    const state = await this.get();
    if (!state) return [];

    let errors = state.system.errors;

    if (resolved !== undefined) {
      errors = errors.filter(error => error.resolved === resolved);
    }

    return errors.sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime());
  }

  async resolveError(id: string): Promise<void> {
    await this.update(state => {
      if (!state) return null;

      const errors = state.system.errors.map(error =>
        error.id === id ? { ...error, resolved: true } : error
      );

      return {
        ...state,
        system: {
          ...state.system,
          errors
        }
      };
    });
  }

  // Usage tracking
  async updateUsage(updates: Partial<UsageStats>): Promise<void> {
    await this.update(state => {
      if (!state) return null;

      return {
        ...state,
        system: {
          ...state.system,
          usage: {
            ...state.system.usage,
            ...updates
          }
        }
      };
    });
  }

  async getUsage(): Promise<UsageStats> {
    const state = await this.get();
    return state?.system.usage || {
      totalWorkflows: 0,
      totalExecutions: 0,
      totalContent: 0,
      totalReviews: 0,
      aiUsage: {
        totalRequests: 0,
        totalTokens: 0,
        totalCost: 0,
        byProvider: {}
      },
      lastReset: new Date().toISOString()
    };
  }

  // Review Assignment operations
  async getReviewAssignment(id: string): Promise<ReviewAssignment | null> {
    const state = await this.get();
    return state?.reviewAssignments[id] || null;
  }

  async setReviewAssignment(assignment: ReviewAssignment): Promise<void> {
    await this.update(state => {
      if (!state) return null;

      return {
        ...state,
        reviewAssignments: {
          ...state.reviewAssignments,
          [assignment.id]: assignment
        }
      };
    });
  }

  async getAllReviewAssignments(): Promise<ReviewAssignment[]> {
    const state = await this.get();
    return state ? Object.values(state.reviewAssignments) : [];
  }

  // Reviewer operations
  async getReviewer(id: string): Promise<Reviewer | null> {
    const state = await this.get();
    return state?.reviewers[id] || null;
  }

  async setReviewer(reviewer: Reviewer): Promise<void> {
    await this.update(state => {
      if (!state) return null;

      return {
        ...state,
        reviewers: {
          ...state.reviewers,
          [reviewer.id]: reviewer
        }
      };
    });
  }

  async getAllReviewers(): Promise<Reviewer[]> {
    const state = await this.get();
    return state ? Object.values(state.reviewers) : [];
  }

  // Artifact Version operations
  async getArtifactVersion(id: string): Promise<ArtifactVersion | null> {
    const state = await this.get();
    return state?.artifactVersions[id] || null;
  }

  async setArtifactVersion(version: ArtifactVersion): Promise<void> {
    await this.update(state => {
      if (!state) return null;

      return {
        ...state,
        artifactVersions: {
          ...state.artifactVersions,
          [version.id]: version
        }
      };
    });
  }

  async getAllArtifactVersions(): Promise<ArtifactVersion[]> {
    const state = await this.get();
    return state ? Object.values(state.artifactVersions) : [];
  }

  // Utility methods
  async getAllWorkflows(): Promise<Workflow[]> {
    const state = await this.get();
    if (!state || !state.workflows) {
      return [];
    }
    return Object.values(state.workflows);
  }

  async getAllExecutions(): Promise<WorkflowExecution[]> {
    const state = await this.get();
    if (!state || !state.executions) {
      console.log(`⚠️ No state or executions found in getAllExecutions`);
      return [];
    }

    const executions = Object.values(state.executions);
    console.log(`📋 getAllExecutions found ${executions.length} executions:`,
      executions.map(e => ({
        id: e?.id || 'undefined',
        workflowId: e?.workflowId || 'undefined',
        status: e?.status || 'undefined',
        isValidObject: typeof e === 'object' && e !== null,
        hasRequiredFields: !!(e?.id && e?.workflowId)
      }))
    );

    return executions;
  }

  async deleteExecution(id: string): Promise<boolean> {
    let deleted = false;

    await this.update(state => {
      if (!state || !state.executions[id]) {
        return state;
      }

      deleted = true;
      const { [id]: removedExecution, ...remainingExecutions } = state.executions;

      return {
        ...state,
        executions: remainingExecutions,
        system: {
          ...state.system,
          usage: {
            ...state.system.usage,
            totalExecutions: Math.max(0, state.system.usage.totalExecutions - 1)
          }
        }
      };
    });

    return deleted;
  }

  async getExecutionsByStatus(status: string): Promise<WorkflowExecution[]> {
    const executions = await this.getAllExecutions();
    return executions.filter(execution => execution.status === status);
  }

  async getContentByStatus(status: string): Promise<ContentItem[]> {
    const state = await this.get();
    if (!state || !state.content) {
      return [];
    }

    return Object.values(state.content).filter(content => content.status === status);
  }

  async getPendingReviews(): Promise<Review[]> {
    const state = await this.get();
    if (!state) return [];

    return Object.values(state.reviews).filter(review => review.status === 'pending');
  }

  // Workflow layout management
  async updateWorkflowLayout(workflowId: string, layout: { nodes: any[]; edges: any[] }): Promise<void> {
    await this.update(state => {
      if (!state || !state.executions[workflowId]) {
        return state;
      }

      return {
        ...state,
        executions: {
          ...state.executions,
          [workflowId]: {
            ...state.executions[workflowId],
            layout,
            updatedAt: new Date().toISOString()
          }
        }
      };
    });
  }

  async updateStepStatus(workflowId: string, stepId: string, status: string, progress?: number): Promise<void> {
    await this.update(state => {
      if (!state || !state.executions[workflowId]) {
        return state;
      }

      const execution = state.executions[workflowId];
      const steps = execution.steps || [];

      const updatedSteps = steps.map(step =>
        step.id === stepId
          ? { ...step, status, progress, updatedAt: new Date().toISOString() }
          : step
      );

      return {
        ...state,
        executions: {
          ...state.executions,
          [workflowId]: {
            ...execution,
            steps: updatedSteps,
            currentStep: status === 'running' ? stepId : execution.currentStep,
            updatedAt: new Date().toISOString()
          }
        }
      };
    });

    // Add event for step status change
    await this.addEvent({
      type: status === 'completed' ? EssentialEventType.WORKFLOW_COMPLETED :
            status === 'failed' ? EssentialEventType.WORKFLOW_FAILED :
            EssentialEventType.WORKFLOW_STARTED,
      data: { workflowId, stepId, status, progress }
    });
  }
}
