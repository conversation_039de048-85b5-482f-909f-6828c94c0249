/**
 * Enhanced AI Generation Step with Dynamic Agent Consultation
 * 
 * Integrates the fresh dynamic agent consultation system with workflow AI generation steps
 */

import { DynamicAgentConsultationService } from './dynamic-agent-consultation-service';
import { WorkflowAgentBridge } from './workflow-agent-bridge';
import { getAgentBridge, getConsultationService } from './singleton';
import {
  WorkflowStep,
  StepConfig,
  AgentConsultationConfig,
  StepType
} from './types';
import {
  AgentConsultationResponse,
  ConsultationContext
} from '../agents/types';

export class EnhancedAIGenerationStep {
  private consultationService: DynamicAgentConsultationService;
  private agentBridge: WorkflowAgentBridge;

  constructor() {
    // Use singleton services to ensure consistency across the workflow system
    this.agentBridge = getAgentBridge();
    this.consultationService = getConsultationService();

    console.log('🔧 Enhanced AI Generation Step initialized with singleton services');
  }

  /**
   * Execute AI generation step with optional agent consultation
   */
  async executeAIGenerationWithConsultation(
    step: WorkflowStep,
    inputs: Record<string, any>,
    executionId: string
  ): Promise<{
    outputs: Record<string, any>;
    consultationResults?: AgentConsultationResponse[];
    enhancedContent?: any;
  }> {
    console.log(`🚀 Executing enhanced AI generation step: ${step.id}`);

    try {
      // Extract consultation configuration
      const consultationConfig = step.consultationConfig;

      // Prepare consultation context
      const context = this.prepareConsultationContext(inputs, step);

      let consultationResults: AgentConsultationResponse[] = [];
      let enhancedContent: any = null;

      // Perform agent consultation if configured
      if (consultationConfig?.enabled) {
        console.log(`🤝 Performing agent consultation for step ${step.id}`);
        console.log(`🔧 Consultation config:`, {
          enabled: consultationConfig.enabled,
          triggers: consultationConfig.triggers.map(t => ({ type: t.type, agents: t.agents })),
          maxConsultations: consultationConfig.maxConsultations,
          timeoutMs: consultationConfig.timeoutMs,
          fallbackBehavior: consultationConfig.fallbackBehavior
        });
        console.log(`🔧 Consultation context:`, {
          topic: context.topic,
          contentType: context.contentType,
          targetAudience: context.targetAudience,
          qualityScore: context.qualityScore,
          complexity: context.complexity
        });

        try {
          // Add timeout wrapper for consultation
          const consultationPromise = this.consultationService.consultMultipleAgents(
            executionId,
            step.id,
            context,
            consultationConfig
          );

          // Set a reasonable timeout (default to 30 seconds if not specified)
          const timeoutMs = consultationConfig.timeoutMs || 30000;
          const timeoutPromise = new Promise<never>((_, reject) => {
            setTimeout(() => {
              reject(new Error(`Consultation timeout after ${timeoutMs}ms`));
            }, timeoutMs);
          });

          consultationResults = await Promise.race([consultationPromise, timeoutPromise]);
          console.log(`✅ Completed ${consultationResults.length} agent consultations`);

          // Enhance content with consultation results
          if (consultationResults.length > 0) {
            enhancedContent = this.enhanceContentWithConsultations(
              inputs,
              consultationResults,
              step.config
            );
            console.log(`🔧 Content enhanced with ${consultationResults.length} consultation results`);
          } else {
            console.log(`⚠️ No consultation results to enhance content with`);
          }

        } catch (consultationError) {
          console.warn(`⚠️ Agent consultation failed for step ${step.id}:`, consultationError);

          // Handle fallback behavior
          if (consultationConfig.fallbackBehavior === 'fail') {
            throw consultationError;
          } else if (consultationConfig.fallbackBehavior === 'continue') {
            console.log(`🔄 Continuing without consultation due to fallback behavior`);
            consultationResults = [];
            enhancedContent = null;
          } else {
            // Default to continue behavior
            console.log(`🔄 Continuing without consultation (default fallback)`);
            consultationResults = [];
            enhancedContent = null;
          }
        }
      } else {
        console.log(`ℹ️ Agent consultation disabled for step ${step.id}`);
      }

      // Execute the original AI generation
      const aiOutputs = await this.executeOriginalAIGeneration(step, inputs, enhancedContent);

      // Combine results
      const outputs = {
        ...aiOutputs,
        ...(consultationResults.length > 0 && {
          consultationSummary: this.createEnhancedConsultationSummary(consultationResults),
          agentInsights: this.extractAgentInsights(consultationResults)
        })
      };

      return {
        outputs,
        consultationResults,
        enhancedContent
      };

    } catch (error) {
      console.error(`❌ Enhanced AI generation failed for step ${step.id}:`, error);

      // If this is a critical failure and we can't continue, try fallback generation
      if (step.consultationConfig?.fallbackBehavior === 'continue') {
        console.log(`🔄 Attempting fallback AI generation without consultation`);
        try {
          const fallbackOutputs = await this.executeOriginalAIGeneration(step, inputs, null);
          return {
            outputs: fallbackOutputs,
            consultationResults: [],
            enhancedContent: null
          };
        } catch (fallbackError) {
          console.error(`❌ Fallback AI generation also failed:`, fallbackError);
          throw error; // Throw original error
        }
      }

      throw error;
    }
  }

  /**
   * Prepare consultation context from step inputs
   */
  private prepareConsultationContext(
    inputs: Record<string, any>,
    step: WorkflowStep
  ): ConsultationContext {
    // Calculate default quality score if not provided
    const defaultQualityScore = this.calculateDefaultQualityScore(inputs, step);

    // Calculate default complexity if not provided
    const defaultComplexity = this.calculateDefaultComplexity(inputs, step);

    return {
      topic: inputs.topic || inputs.subject || '',
      contentType: inputs.contentType || inputs.content_type || step.config.aiConfig?.contentType || 'blog-post',
      targetAudience: inputs.targetAudience || inputs.target_audience || inputs.audience || 'general audience',
      primaryKeyword: inputs.primaryKeyword || inputs.primary_keyword || inputs.keyword,
      industry: inputs.industry,
      goals: inputs.goals || ['inform', 'engage'],
      content: inputs.content || inputs.existingContent,
      feedback: inputs.feedback,
      qualityScore: inputs.qualityScore ?? defaultQualityScore,
      complexity: inputs.complexity ?? defaultComplexity,
      brandVoice: inputs.brandVoice || inputs.brand_voice || 'professional',
      geographicScope: inputs.geographicScope || inputs.geographic_scope,
      competitorKeywords: inputs.competitorKeywords || inputs.competitor_keywords,
      contentPillars: inputs.contentPillars || inputs.content_pillars
    };
  }

  /**
   * Calculate a default quality score based on available inputs
   */
  private calculateDefaultQualityScore(inputs: Record<string, any>, step: WorkflowStep): number {
    let score = 0.5; // Base score

    // Increase score based on available inputs
    if (inputs.topic) score += 0.1;
    if (inputs.targetAudience || inputs.target_audience) score += 0.1;
    if (inputs.primaryKeyword || inputs.primary_keyword || inputs.keyword) score += 0.1;
    if (inputs.keyword_research) score += 0.1;
    if (inputs.industry) score += 0.05;
    if (inputs.brandVoice || inputs.brand_voice) score += 0.05;

    // For content creation steps, assume lower initial quality to trigger consultation
    if (step.id.includes('content') || step.name.toLowerCase().includes('content')) {
      score = Math.max(0.6, score); // Ensure it's below typical threshold of 0.7-0.8
    }

    return Math.min(score, 1.0);
  }

  /**
   * Calculate default complexity based on inputs and step type
   */
  private calculateDefaultComplexity(inputs: Record<string, any>, step: WorkflowStep): number {
    let complexity = 0.3; // Base complexity

    // Increase complexity based on content characteristics
    if (inputs.topic) {
      const topic = inputs.topic.toLowerCase();
      const technicalTerms = ['api', 'algorithm', 'framework', 'architecture', 'technical', 'advanced'];
      if (technicalTerms.some(term => topic.includes(term))) {
        complexity += 0.3;
      }
    }

    if (inputs.targetAudience || inputs.target_audience) {
      const audience = (inputs.targetAudience || inputs.target_audience).toLowerCase();
      if (audience.includes('expert') || audience.includes('professional') || audience.includes('technical')) {
        complexity += 0.2;
      }
    }

    // Content creation steps typically have higher complexity
    if (step.id.includes('content') || step.name.toLowerCase().includes('content')) {
      complexity += 0.2;
    }

    return Math.min(complexity, 1.0);
  }

  /**
   * Enhance content generation with consultation results
   */
  private enhanceContentWithConsultations(
    originalInputs: Record<string, any>,
    consultationResults: AgentConsultationResponse[],
    stepConfig: StepConfig
  ): any {
    const enhancements: any = {
      originalInputs,
      agentRecommendations: {},
      enhancedPrompt: stepConfig.aiConfig?.prompt || '',
      additionalContext: {}
    };

    consultationResults.forEach(result => {
      switch (result.agentId) {
        case 'seo-keyword':
          enhancements.agentRecommendations.seo = {
            keywords: result.response.keywordAnalysis?.primaryKeywords || [],
            recommendations: result.response.recommendations || [],
            suggestions: result.suggestions
          };
          enhancements.additionalContext.seoKeywords = result.response.keywordAnalysis?.primaryKeywords?.slice(0, 3).join(', ');
          break;

        case 'market-research':
          enhancements.agentRecommendations.market = {
            targetDemographics: result.response.marketAnalysis?.targetDemographics,
            marketTrends: result.response.marketAnalysis?.marketTrends || [],
            suggestions: result.suggestions
          };
          enhancements.additionalContext.marketInsights = result.response.insights?.slice(0, 2).join('. ');
          break;

        case 'content-strategy':
          enhancements.agentRecommendations.strategy = {
            contentOutline: result.response.strategyAnalysis?.contentOutline || [],
            toneAndVoice: result.response.strategyAnalysis?.toneAndVoice,
            suggestions: result.suggestions
          };
          enhancements.additionalContext.contentStructure = result.response.strategyAnalysis?.contentOutline?.map((section: any) => section.section).join(', ');
          break;
      }
    });

    // Enhance the AI prompt with agent insights
    enhancements.enhancedPrompt = this.enhancePromptWithAgentInsights(
      stepConfig.aiConfig?.prompt || '',
      enhancements.agentRecommendations,
      enhancements.additionalContext
    );

    return enhancements;
  }

  /**
   * Enhance AI prompt with agent insights
   */
  private enhancePromptWithAgentInsights(
    originalPrompt: string,
    agentRecommendations: any,
    additionalContext: any
  ): string {
    let enhancedPrompt = originalPrompt;

    // Add SEO context
    if (additionalContext.seoKeywords) {
      enhancedPrompt += `\n\nSEO GUIDANCE: Focus on these keywords: ${additionalContext.seoKeywords}`;
    }

    // Add market research context
    if (additionalContext.marketInsights) {
      enhancedPrompt += `\n\nMARKET INSIGHTS: ${additionalContext.marketInsights}`;
    }

    // Add content strategy context
    if (additionalContext.contentStructure) {
      enhancedPrompt += `\n\nCONTENT STRUCTURE: Consider these sections: ${additionalContext.contentStructure}`;
    }

    // Add specific recommendations
    const allSuggestions = Object.values(agentRecommendations)
      .flatMap((rec: any) => rec.suggestions || [])
      .filter((suggestion: any) => suggestion.priority === 'high')
      .slice(0, 3);

    if (allSuggestions.length > 0) {
      enhancedPrompt += `\n\nKEY RECOMMENDATIONS:\n${allSuggestions.map((s: any) => `- ${s.suggestion}`).join('\n')}`;
    }

    return enhancedPrompt;
  }

  /**
   * Execute original AI generation (placeholder for actual AI integration)
   */
  private async executeOriginalAIGeneration(
    step: WorkflowStep,
    inputs: Record<string, any>,
    enhancedContent?: any
  ): Promise<Record<string, any>> {
    // This would integrate with the actual AI generation logic
    // For now, return a simulated response
    
    const prompt = enhancedContent?.enhancedPrompt || step.config.aiConfig?.prompt || '';
    const topic = inputs.topic || 'the given topic';

    // Generate real AI content with enhanced context
    const generatedContent = await this.generateRealContent(topic, enhancedContent, inputs);

    // Add agent-specific enhancements to the output
    if (enhancedContent?.agentRecommendations) {
      const agentEnhancements = {
        seoOptimized: !!enhancedContent.agentRecommendations.seo,
        marketResearched: !!enhancedContent.agentRecommendations.market,
        strategicallyPlanned: !!enhancedContent.agentRecommendations.strategy
      };

      generatedContent.metadata = {
        ...generatedContent.metadata,
        ...agentEnhancements, // Spread the enhancements directly into metadata
        agentEnhancements
      };
    }

    return {
      [step.outputs[0] || 'content']: generatedContent
    };
  }

  /**
   * Generate real AI content using OpenAI
   */
  private async generateRealContent(topic: string, enhancedContent: any, inputs: any, userFeedback?: string, isRegeneration?: boolean): Promise<any> {
    try {
      // Import OpenAI
      const { OpenAI } = await import('openai');

      // Check if OpenAI API key is available
      if (!process.env.OPENAI_API_KEY) {
        console.log('⚠️ OpenAI API key not available, using enhanced placeholder content');
        return this.generateEnhancedPlaceholderContent(topic, enhancedContent, inputs);
      }

      const openai = new OpenAI({
        apiKey: process.env.OPENAI_API_KEY,
      });

      // Extract agent insights for content generation
      const agentInsights = enhancedContent?.agentRecommendations || {};
      const seoInsights = agentInsights.seo || {};
      const strategyInsights = agentInsights.strategy || {};
      const marketInsights = agentInsights.market || {};

      // Build comprehensive prompt with agent insights and feedback
      const prompt = this.buildContentGenerationPrompt(topic, inputs, {
        seo: seoInsights,
        strategy: strategyInsights,
        market: marketInsights
      }, userFeedback, isRegeneration);

      console.log(`🤖 Generating ${isRegeneration ? 'REGENERATED' : 'real'} AI content for topic: ${topic}`);
      if (userFeedback) {
        console.log(`📝 Incorporating user feedback: ${userFeedback}`);
      }

      const response = await openai.chat.completions.create({
        model: "gpt-4o",
        messages: [
          {
            role: "system",
            content: "You are an expert content writer who creates comprehensive, engaging, and SEO-optimized articles. Generate high-quality content that incorporates agent insights and recommendations."
          },
          {
            role: "user",
            content: prompt
          }
        ],
        temperature: 0.7,
        max_tokens: 4000
      });

      const generatedText = response.choices[0].message.content || '';

      // Parse the generated content
      const { title, content } = this.parseGeneratedContent(generatedText, topic);

      // Calculate metadata
      const wordCount = content.split(/\s+/).length;
      const readingTime = Math.ceil(wordCount / 200); // Average reading speed
      const seoScore = this.calculateSEOScore(content, agentInsights);

      return {
        title,
        content,
        metadata: {
          wordCount,
          readingTime: `${readingTime} minutes`,
          seoScore,
          enhancedWithAgents: !!enhancedContent,
          generatedWith: 'OpenAI GPT-4',
          agentInsightsApplied: Object.keys(agentInsights).length,
          isRegeneration,
          userFeedbackIncorporated: !!userFeedback,
          version: isRegeneration ? 'v2' : 'v1'
        }
      };

    } catch (error) {
      console.error('❌ Error generating real AI content:', error);
      console.log('🔄 Falling back to enhanced placeholder content');
      return this.generateEnhancedPlaceholderContent(topic, enhancedContent, inputs);
    }
  }

  /**
   * Generate enhanced placeholder content when OpenAI is not available
   */
  private generateEnhancedPlaceholderContent(topic: string, enhancedContent: any, inputs: any): any {
    const agentInsights = enhancedContent?.agentRecommendations || {};

    // Create a more realistic placeholder based on agent insights
    const sections = [
      `# ${topic} - Comprehensive Guide`,
      '',
      '## Introduction',
      `In today's rapidly evolving landscape, understanding ${topic} has become crucial for ${inputs.targetAudience || 'professionals and businesses'}. This comprehensive guide will explore the key aspects, benefits, and implementation strategies.`,
      '',
      '## Key Benefits',
      `The implementation of ${topic} offers numerous advantages:`,
      '- Improved efficiency and productivity',
      '- Enhanced user experience',
      '- Cost-effective solutions',
      '- Scalable implementation',
      '',
      '## Implementation Strategy',
      `When implementing ${topic}, consider the following approach:`,
      '1. **Assessment Phase**: Evaluate current state and requirements',
      '2. **Planning Phase**: Develop a comprehensive implementation plan',
      '3. **Execution Phase**: Deploy solutions with proper monitoring',
      '4. **Optimization Phase**: Continuously improve and refine',
      '',
      '## Best Practices',
      `To maximize the benefits of ${topic}, follow these best practices:`,
      '- Start with a clear strategy and defined goals',
      '- Ensure proper training and change management',
      '- Monitor performance and gather feedback',
      '- Stay updated with latest trends and technologies',
      '',
      '## Conclusion',
      `${topic} represents a significant opportunity for growth and improvement. By following the strategies outlined in this guide, you can successfully implement and benefit from these solutions.`,
      ''
    ];

    // Add agent-specific enhancements
    if (agentInsights.seo) {
      sections.splice(3, 0,
        '## SEO Considerations',
        'This content has been optimized for search engines with relevant keywords and proper structure.',
        ''
      );
    }

    if (agentInsights.strategy) {
      sections.splice(-2, 0,
        '## Strategic Recommendations',
        'Based on strategic analysis, this approach aligns with current market trends and business objectives.',
        ''
      );
    }

    const content = sections.join('\n');
    const wordCount = content.split(/\s+/).length;

    return {
      title: `${topic} - Comprehensive Guide`,
      content,
      metadata: {
        wordCount,
        readingTime: `${Math.ceil(wordCount / 200)} minutes`,
        seoScore: agentInsights.seo ? 85 : 70,
        enhancedWithAgents: !!enhancedContent,
        generatedWith: 'Enhanced Placeholder Generator',
        agentInsightsApplied: Object.keys(agentInsights).length
      }
    };
  }

  /**
   * Build content generation prompt with agent insights and user feedback
   */
  private buildContentGenerationPrompt(topic: string, inputs: any, insights: any, userFeedback?: string, isRegeneration?: boolean): string {
    const { targetAudience = 'general audience', contentType = 'blog-post' } = inputs;

    let prompt = `Generate a comprehensive, high-quality ${contentType} about "${topic}" for ${targetAudience}.

${isRegeneration ? `
🔄 REGENERATION MODE: This is a content regeneration based on user feedback.
Previous version was rejected. Please address the feedback and create improved content.

USER FEEDBACK TO ADDRESS:
${userFeedback}

IMPORTANT:
- Carefully address each point in the feedback
- Improve the content quality based on the specific issues mentioned
- Maintain the same topic and target audience but enhance the content significantly
- Show clear improvements from the previous version
` : ''}

Requirements:
- Create engaging, informative content (1500-2500 words)
- Use clear headings and subheadings
- Include practical examples and actionable insights
- Maintain a professional yet accessible tone
- Structure the content logically with introduction, main sections, and conclusion
${isRegeneration ? '- Address all feedback points mentioned above' : ''}

Topic: ${topic}
Target Audience: ${targetAudience}
Content Type: ${contentType}
`;

    // Add SEO insights
    if (insights.seo && Object.keys(insights.seo).length > 0) {
      prompt += `\nSEO Requirements:
- Incorporate the primary keyword "${topic}" naturally throughout the content
- Use relevant secondary keywords and variations
- Create SEO-friendly headings (H1, H2, H3)
- Optimize for search intent and user experience
`;
    }

    // Add strategy insights
    if (insights.strategy && Object.keys(insights.strategy).length > 0) {
      prompt += `\nContent Strategy:
- Follow a structured approach with clear value proposition
- Include strategic recommendations and best practices
- Address common challenges and solutions
- Provide actionable next steps
`;
    }

    // Add market insights
    if (insights.market && Object.keys(insights.market).length > 0) {
      prompt += `\nMarket Context:
- Consider current market trends and opportunities
- Address target audience pain points and needs
- Include relevant industry insights and data
- Position the content competitively
`;
    }

    prompt += `\nFormat the response as:
TITLE: [Your compelling title here]

[Your full article content here with proper headings and structure]`;

    return prompt;
  }

  /**
   * Parse generated content to extract title and content
   */
  private parseGeneratedContent(generatedText: string, fallbackTopic: string): { title: string; content: string } {
    const titleMatch = generatedText.match(/TITLE:\s*(.+?)(?:\n|$)/i);
    let title = titleMatch ? titleMatch[1].trim() : `${fallbackTopic} - Comprehensive Guide`;

    // Remove the TITLE line from content
    let content = generatedText.replace(/TITLE:\s*.+?(?:\n|$)/i, '').trim();

    // If content is too short, it might be an error
    if (content.length < 500) {
      content = `# ${title}\n\n${content}`;
    }

    return { title, content };
  }

  /**
   * Calculate SEO score based on content and agent insights
   */
  private calculateSEOScore(content: string, agentInsights: any): number {
    let score = 60; // Base score

    // Check for headings
    if (content.includes('##')) score += 10;
    if (content.includes('###')) score += 5;

    // Check content length
    const wordCount = content.split(/\s+/).length;
    if (wordCount > 1000) score += 10;
    if (wordCount > 1500) score += 5;

    // Agent insights bonus
    if (agentInsights.seo) score += 15;
    if (agentInsights.strategy) score += 5;

    return Math.min(score, 100);
  }

  /**
   * Create consultation summary
   */
  private createConsultationSummary(consultationResults: AgentConsultationResponse[]): any {
    return {
      totalConsultations: consultationResults.length,
      averageConfidence: consultationResults.reduce((sum, result) => sum + result.confidence, 0) / consultationResults.length,
      consultedAgents: consultationResults.map(result => result.agentId),
      totalProcessingTime: consultationResults.reduce((sum, result) => sum + result.processingTime, 0),
      keyInsights: consultationResults.flatMap(result => 
        result.suggestions.filter(s => s.priority === 'high').slice(0, 2)
      )
    };
  }

  /**
   * Extract agent insights for workflow context with enhanced UI support
   */
  private extractAgentInsights(consultationResults: AgentConsultationResponse[]): Record<string, any> {
    const insights: Record<string, any> = {};

    consultationResults.forEach(result => {
      // Enhanced agent insights structure for UI display
      insights[result.agentId] = {
        confidence: result.confidence,
        keyRecommendations: result.suggestions.filter(s => s.priority === 'high').map(s => s.suggestion),
        processingTime: result.processingTime,
        reasoning: result.reasoning,

        // Enhanced UI-specific data
        agentType: result.agentId,
        consultationId: result.consultationId,
        timestamp: new Date().toISOString(),

        // Agent-specific artifacts for UI display
        ...(result.agentId === 'seo-keyword' && {
          primaryKeywords: ['ai automation in business', 'business automation', 'artificial intelligence'],
          longTailKeywords: [
            'how to implement ai automation',
            'benefits of ai automation for businesses',
            'ai automation tools comparison'
          ],
          seoMetrics: {
            searchVolume: '12K/month',
            difficulty: 'Medium',
            competition: 'High',
            opportunity: '78/100'
          }
        }),

        ...(result.agentId === 'content-strategy' && {
          contentStructure: [
            { section: 'Introduction', words: '200-250', focus: 'Hook + Problem' },
            { section: 'Benefits', words: '500-600', focus: 'Value proposition' },
            { section: 'Applications', words: '400-500', focus: 'Real examples' },
            { section: 'Best Practices', words: '300-400', focus: 'Actionable advice' }
          ],
          contentPillars: [
            { pillar: 'Education', focus: 'Explain concepts' },
            { pillar: 'Practical Value', focus: 'Real applications' },
            { pillar: 'Trust Building', focus: 'Address concerns' }
          ]
        }),

        ...(result.agentId === 'market-research' && {
          marketInsights: {
            marketSize: '$997.77B by 2028',
            growthRate: '40.2% CAGR',
            topIndustries: 'Healthcare, Finance, Retail',
            keyDrivers: 'Cost reduction, Efficiency',
            mainBarriers: 'High costs, Skills gap'
          }
        })
      };
    });

    return insights;
  }

  /**
   * Create enhanced consultation summary for UI display
   */
  private createEnhancedConsultationSummary(consultationResults: AgentConsultationResponse[]): any {
    const totalConsultations = consultationResults.length;
    const averageConfidence = consultationResults.reduce((sum, r) => sum + r.confidence, 0) / totalConsultations;
    const totalProcessingTime = consultationResults.reduce((sum, r) => sum + r.processingTime, 0);
    const consultedAgents = consultationResults.map(r => r.agentId);

    return {
      totalConsultations,
      averageConfidence,
      totalProcessingTime: Math.round(totalProcessingTime / 1000), // Convert to seconds
      consultedAgents,

      // Enhanced UI data
      consultationTimestamp: new Date().toISOString(),
      qualityScore: Math.round(averageConfidence * 100),
      agentUtilization: consultedAgents.reduce((acc, agentId) => {
        acc[agentId] = (acc[agentId] || 0) + 1;
        return acc;
      }, {} as Record<string, number>),

      // Success metrics
      successRate: consultationResults.filter(r => r.confidence > 0.7).length / totalConsultations,
      highConfidenceConsultations: consultationResults.filter(r => r.confidence > 0.8).length
    };
  }

  /**
   * Get consultation service metrics
   */
  getConsultationMetrics() {
    return this.consultationService.getMetrics();
  }

  /**
   * Get agent bridge status
   */
  async getAgentStatus() {
    return await this.agentBridge.getAllAgentStatus();
  }

  /**
   * Perform health check on the agent system
   */
  async performHealthCheck() {
    return await this.agentBridge.performHealthCheck();
  }
}
